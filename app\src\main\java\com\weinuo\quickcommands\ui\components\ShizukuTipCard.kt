package com.weinuo.quickcommands.ui.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Info
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.permission.GlobalPermissionManager
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.ui.theme.manager.LocalThemeContext
import com.weinuo.quickcommands.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands.ui.theme.skyblue.SkyBlueStyleConfiguration

/**
 * Shizuku权限提示卡片组件
 * 
 * 一次性显示的提示卡片，用于引导用户开启Shizuku权限以获得更好的优化效果。
 * 支持动画效果和状态保存，关闭后不再显示。
 * 
 * @param visible 是否显示卡片
 * @param onDismiss 关闭卡片的回调
 * @param onOpenShizuku 开启Shizuku权限的回调
 * @param modifier 修饰符
 */
@Composable
fun ShizukuTipCard(
    visible: Boolean,
    onDismiss: () -> Unit,
    onOpenShizuku: () -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }
    val themeContext = LocalThemeContext.current

    // 获取动态卡片样式配置（仅在天空蓝主题下使用）
    val cardStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    } else {
        themeContext.styleConfiguration.cardStyle
    }

    // 根据主题选择背景颜色和样式
    val backgroundColor = if (themeManager.getCurrentThemeId() == "sky_blue") {
        MaterialTheme.colorScheme.surfaceContainerLow // 天空蓝主题使用白色背景
    } else {
        MaterialTheme.colorScheme.primaryContainer // 其他主题保持原样
    }

    val elevation = if (themeManager.getCurrentThemeId() == "sky_blue") {
        0.dp // 天空蓝主题无阴影
    } else {
        4.dp // 其他主题保持原样
    }

    AnimatedVisibility(
        visible = visible,
        enter = slideInVertically(
            initialOffsetY = { -it }
        ) + fadeIn(),
        exit = slideOutVertically(
            targetOffsetY = { -it }
        ) + fadeOut(),
        modifier = modifier
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            colors = CardDefaults.cardColors(
                containerColor = backgroundColor
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = elevation
            ),
            shape = RoundedCornerShape(cardStyle.defaultCornerRadius)
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(cardStyle.getSettingsPaddingValues())
            ) {
                // 主要内容区域
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(end = 48.dp) // 为右上角关闭按钮留出空间
                ) {
                    // 根据主题决定是否显示图标
                    if (themeManager.getCurrentThemeId() != "sky_blue") {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Info,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.primary,
                                modifier = Modifier.size(24.dp)
                            )
                            Spacer(modifier = Modifier.width(12.dp))
                            Text(
                                text = "获得更好的优化效果",
                                style = MaterialTheme.typography.titleSmall,
                                color = MaterialTheme.colorScheme.primary
                            )
                        }
                    } else {
                        // 天空蓝主题：无图标，直接显示标题
                        Text(
                            text = "获得更好的优化效果",
                            style = MaterialTheme.typography.titleSmall,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }

                    Spacer(modifier = Modifier.height(4.dp))

                    Text(
                        text = "开启Shizuku权限可以精准停止后台应用",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                // 右上角关闭按钮
                IconButton(
                    onClick = onDismiss,
                    modifier = Modifier.align(Alignment.TopEnd)
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "关闭",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                // 右下角开启按钮
                TextButton(
                    onClick = onOpenShizuku,
                    modifier = Modifier.align(Alignment.BottomEnd)
                ) {
                    Text("开启")
                }
            }
        }
    }
}

/**
 * 带有权限管理集成的Shizuku提示卡片
 *
 * 自动处理Shizuku权限申请逻辑的便捷组件
 *
 * @param visible 是否显示卡片
 * @param onDismiss 关闭卡片的回调
 * @param modifier 修饰符
 */
@Composable
fun ShizukuTipCardWithPermission(
    visible: Boolean,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val globalPermissionManager = remember { GlobalPermissionManager.getInstance(context) }

    ShizukuTipCard(
        visible = visible,
        onDismiss = onDismiss,
        onOpenShizuku = {
            // 直接申请Shizuku权限，跳过确认对话框
            globalPermissionManager.requestPermissionAfterConfirmation(
                GlobalPermissionManager.PermissionType.SHIZUKU
            )
        },
        modifier = modifier
    )
}
